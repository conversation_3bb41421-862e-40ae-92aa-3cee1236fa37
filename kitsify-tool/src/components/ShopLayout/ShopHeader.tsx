'use client';

import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import React, { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import { useCart } from '@/contexts/CartContext';
import { authService } from '@/services/auth';

const ShopHeader: React.FC = () => {
  const t = useTranslations('ShopLayout');
  const router = useRouter();
  const { cartCount } = useCart();
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [userDropdownOpen, setUserDropdownOpen] = useState(false);

  // Check authentication status
  useEffect(() => {
    const checkAuth = async () => {
      const isAuth = await authService.checkAuthStatus();
      setIsAuthenticated(isAuth);
    };
    checkAuth();
  }, []);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (!target.closest('#user-dropdown-button') && !target.closest('#user-dropdown')) {
        setUserDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleLogout = async () => {
    try {
      const result = await authService.logout();
      if (result.success) {
        setIsAuthenticated(false);
        setUserDropdownOpen(false);
        router.push('/sign-in');
      } else {
        toast.error(result.error);
      }
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  return (
    <header className="bg-white shadow-md">
      <div className="container mx-auto px-4 py-4">
        <div className="relative flex items-center justify-between">
          {/* Left spacer */}
          <div className="flex-1" />

          {/* Centered Logo */}
          <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 transform">
            <button type="button" onClick={() => router.push('/')}>
              <Image
                src="/assets/images/kitsify_rect.png"
                alt="Kitsify Logo"
                width={150}
                height={40}
                priority
              />
            </button>
          </div>

          {/* Right side icons */}
          <div className="flex flex-1 items-center justify-end space-x-4">
            <button
              type="button"
              onClick={() => router.push('/cart')}
              className="relative rounded-full p-2 text-gray-600 hover:bg-gray-100"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="size-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z"
                />
              </svg>
              {cartCount > 0 && (
                <span className="absolute -right-1 -top-1 flex size-5 items-center justify-center rounded-full bg-red-600 text-xs text-white">
                  {cartCount}
                </span>
              )}
            </button>

            {isAuthenticated
              ? (
                  <div className="relative">
                    <button
                      type="button"
                      id="user-dropdown-button"
                      onClick={() => setUserDropdownOpen(!userDropdownOpen)}
                      className="flex items-center rounded-full text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                    >
                      <span className="sr-only">Open user menu</span>
                      <div className="flex items-center">
                        <div className="flex size-8 items-center justify-center rounded-full bg-blue-500 text-white">
                          <span className="font-medium">U</span>
                        </div>
                        <span className="ml-2 hidden text-sm font-medium text-gray-700 md:block">
                          User
                        </span>
                        <svg
                          className="ml-1 size-4 text-gray-400"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M19 9l-7 7-7-7"
                          />
                        </svg>
                      </div>
                    </button>

                    {/* Dropdown menu */}
                    {userDropdownOpen && (
                      <div
                        id="user-dropdown"
                        className="absolute right-0 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black/5 focus:outline-none"
                      >
                        <button
                          type="button"
                          onClick={handleLogout}
                          className="block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
                        >
                          {t('logout')}
                        </button>
                      </div>
                    )}
                  </div>
                )
              : (
                  <button
                    type="button"
                    onClick={() => router.push('/sign-in')}
                    className="rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700"
                  >
                    {t('login')}
                  </button>
                )}
          </div>
        </div>
      </div>
    </header>
  );
};

export default ShopHeader;