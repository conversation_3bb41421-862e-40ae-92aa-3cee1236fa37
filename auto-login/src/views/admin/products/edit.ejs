<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Admin - Edit Product</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/toastify-js@1.12.0/src/toastify.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/toastify-js@1.12.0/src/toastify.min.css">
  </head>
  <body class="bg-gray-100 min-h-screen p-8">
    <div class="max-w-4xl mx-auto">
      <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900">Edit Product</h1>
        <div class="space-x-2">
          <button id="backBtn" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
            Back to Product
          </button>
        </div>
      </div>

      <div class="bg-white shadow rounded-lg overflow-hidden">
        <div class="p-6">
          <div id="errorMessage" class="hidden mb-4 p-4 text-red-700 bg-red-100 rounded-md"></div>

          <form id="editProductForm" class="space-y-6">
            <!-- Basic Information -->
            <div>
              <h2 class="text-xl font-semibold mb-4">Basic Information</h2>
              
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label for="name" class="block text-sm font-medium text-gray-700">Name</label>
                  <input 
                    type="text" 
                    id="name" 
                    name="name" 
                    required
                    class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  />
                </div>

                <div>
                  <label for="category_id" class="block text-sm font-medium text-gray-700">Category</label>
                  <select 
                    id="category_id" 
                    name="category_id"
                    class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  >
                    <option value="">Select a category</option>
                    <!-- Categories will be loaded dynamically -->
                  </select>
                </div>

                <div>
                  <label for="original_price" class="block text-sm font-medium text-gray-700">Original Price</label>
                  <div class="mt-1 relative rounded-md shadow-sm">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <span class="text-gray-500 sm:text-sm">$</span>
                    </div>
                    <input 
                      type="number" 
                      step="0.01" 
                      min="0" 
                      id="original_price" 
                      name="original_price" 
                      required
                      class="pl-7 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                    />
                  </div>
                </div>

                <div>
                  <label for="discount_price" class="block text-sm font-medium text-gray-700">Discount Price (Optional)</label>
                  <div class="mt-1 relative rounded-md shadow-sm">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <span class="text-gray-500 sm:text-sm">$</span>
                    </div>
                    <input 
                      type="number" 
                      step="0.01" 
                      min="0" 
                      id="discount_price" 
                      name="discount_price"
                      class="pl-7 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                    />
                  </div>
                </div>

                <div>
                  <label for="quantity" class="block text-sm font-medium text-gray-700">Quantity</label>
                  <input 
                    type="number" 
                    min="0" 
                    id="quantity" 
                    name="quantity" 
                    required
                    class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  />
                </div>

                <div>
                  <label for="image_url" class="block text-sm font-medium text-gray-700">Image URL</label>
                  <input 
                    type="url" 
                    id="image_url" 
                    name="image_url"
                    class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  />
                </div>
              </div>
            </div>

            <!-- Description -->
            <div>
              <h2 class="text-xl font-semibold mb-4">Description</h2>
              <div>
                <label for="description" class="block text-sm font-medium text-gray-700">Product Description</label>
                <textarea 
                  id="description" 
                  name="description" 
                  rows="4"
                  class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                ></textarea>
              </div>
            </div>

            <!-- Features -->
            <div>
              <h2 class="text-xl font-semibold mb-4">Features</h2>
              <div class="space-y-2">
                <div id="features-container">
                  <!-- Features will be added here dynamically -->
                </div>
                <button 
                  type="button" 
                  id="addFeatureBtn"
                  class="mt-2 inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  Add Feature
                </button>
              </div>
            </div>

            <!-- Submit Button -->
            <div class="flex justify-end">
              <button 
                type="submit"
                class="px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Save Changes
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <script>
      // Get product ID from server-rendered data
      const productId = `<%- id %>`; // Use the ID passed from the server
      let features = [];
      
      // Setup axios with credentials
      function setupAxios() {
        // Set axios to send cookies with all requests
        axios.defaults.withCredentials = true;
        return true;
      }
      
      // Load categories
      async function loadCategories(selectedCategoryId = null) {
        try {
          const response = await axios.get('/products/categories');
          const categories = response.data;
          const categorySelect = document.getElementById('category_id');
          
          categories.forEach(category => {
            const option = document.createElement('option');
            option.value = category.id;
            option.textContent = category.name;
            if (selectedCategoryId && category.id === selectedCategoryId) {
              option.selected = true;
            }
            categorySelect.appendChild(option);
          });
        } catch (error) {
          console.error('Error loading categories:', error);
        }
      }

      // Check authentication and admin role
      async function checkAuth() {
        try {
          const response = await axios.get('/auth/status', { withCredentials: true });
          if (!response.data.isAuthenticated) {
            window.location.href = '/login';
            return false;
          }

          // Check if user has admin role
          if (response.data.user && response.data.user.role !== 'admin') {
            // User is authenticated but not an admin
            window.location.href = '/login';
            return false;
          }

          return true;
        } catch (error) {
          window.location.href = '/login';
          return false;
        }
      }

      // Show toast notification
      function showToast(message, type = 'success') {
        const backgroundColor = type === 'success' ? '#48bb78' : '#f56565';

        Toastify({
          text: message,
          duration: 3000,
          close: true,
          gravity: "top",
          position: "right",
          backgroundColor,
          stopOnFocus: true
        }).showToast();
      }

      // Show error message
      function showError(message) {
        const errorMessage = document.getElementById('errorMessage');
        errorMessage.textContent = message;
        errorMessage.classList.remove('hidden');
        showToast(message, 'error');
      }

      // Fetch product details
      async function fetchProduct(id) {
        if (!setupAxios()) return;

        try {
          const response = await axios.get(`/products/${id}`);
          return response.data;
        } catch (error) {
          if (error.response && error.response.status === 401) {
            window.location.href = '/login';
          } else {
            const message = error.response?.data?.message || 'Failed to fetch product details. Please try again.';
            showError(message);
          }
          return null;
        }
      }

      // Populate form with product data
      async function populateForm(product) {
        document.getElementById('name').value = product.name || '';
        document.getElementById('original_price').value = product.original_price || '';
        document.getElementById('discount_price').value = product.discount_price || '';
        document.getElementById('quantity').value = product.quantity || 0;
        document.getElementById('image_url').value = product.image_url || '';
        document.getElementById('description').value = product.description || '';

        // Load categories and select the current one if it exists
        const categoryId = product.category?.id;
        await loadCategories(categoryId);

        // Set features
        features = product.features || [];
        renderFeatures();
      }

      // Render features inputs
      function renderFeatures() {
        const container = document.getElementById('features-container');
        container.innerHTML = '';

        features.forEach((feature, index) => {
          const featureRow = document.createElement('div');
          featureRow.className = 'flex items-center space-x-2';
          
          featureRow.innerHTML = `
            <input 
              type="text" 
              name="features[]" 
              value="${feature}" 
              class="flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            />
            <button 
              type="button" 
              class="remove-feature text-red-600 hover:text-red-800" 
              data-index="${index}"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
              </svg>
            </button>
          `;
          
          container.appendChild(featureRow);
        });

        // Add event listeners for remove buttons
        document.querySelectorAll('.remove-feature').forEach(button => {
          button.addEventListener('click', function() {
            const index = parseInt(this.getAttribute('data-index'));
            features.splice(index, 1);
            renderFeatures();
          });
        });
      }

      // Add new feature
      function addFeature() {
        features.push('');
        renderFeatures();
        
        // Focus the last input
        const inputs = document.querySelectorAll('input[name="features[]"]');
        if (inputs.length > 0) {
          inputs[inputs.length - 1].focus();
        }
      }

      // Update product
      async function updateProduct(formData) {
        if (!setupAxios()) return;

        try {
          // Get updated features from inputs
          const featureInputs = document.querySelectorAll('input[name="features[]"]');
          const updatedFeatures = Array.from(featureInputs).map(input => input.value.trim()).filter(Boolean);
          
          // Create payload
          const payload = {
            name: formData.get('name'),
            original_price: parseFloat(formData.get('original_price')),
            quantity: parseInt(formData.get('quantity')),
            description: formData.get('description'),
            features: updatedFeatures,
          };
          
          // Add category_id if selected
          const categoryId = formData.get('category_id');
          if (categoryId) {
            payload.category_id = parseInt(categoryId);
          }
          
          // Add optional fields if they have values
          const discountPrice = formData.get('discount_price');
          if (discountPrice) {
            payload.discount_price = parseFloat(discountPrice);
          }
          
          const imageUrl = formData.get('image_url');
          if (imageUrl) {
            payload.image_url = imageUrl;
          }

          await axios.patch(`/products/${productId}`, payload);
          showToast('Product updated successfully');
          
          // Redirect back to product details page after a short delay
          setTimeout(() => {
            window.location.href = `/admin/products/${productId}`;
          }, 1500);
        } catch (error) {
          if (error.response && error.response.status === 401) {
            window.location.href = '/login';
          } else {
            const message = error.response?.data?.message || 'Failed to update product. Please try again.';
            showError(message);
          }
        }
      }

      // Event listeners
      document.addEventListener('DOMContentLoaded', async () => {
        // Initial setup
        setupAxios();
        await checkAuth();

        // Fetch and populate product details
        if (productId) {
          const product = await fetchProduct(productId);
          if (product) {
            await populateForm(product);
          }
        } else {
          showError('Product ID not found in URL');
        }

        // Add feature button
        document.getElementById('addFeatureBtn').addEventListener('click', addFeature);

        // Back button
        document.getElementById('backBtn').addEventListener('click', () => {
          window.location.href = `/admin/products/${productId}`;
        });

        // Form submission
        document.getElementById('editProductForm').addEventListener('submit', async (e) => {
          e.preventDefault();
          const formData = new FormData(e.target);
          await updateProduct(formData);
        });
      });
    </script>
  </body>
</html> 