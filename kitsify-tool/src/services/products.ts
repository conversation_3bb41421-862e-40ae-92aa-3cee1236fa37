import { api } from './api';
import type { Category } from './categories';

export type Product = {
  id: number;
  name: string;
  description?: string;
  original_price: number;
  discount_price: number;
  quantity: number;
  category?: Category;
  image_url?: string;
  features: string[];
  created_at: Date;
  updated_at: Date;
};

export type ProductsResponse = {
  data: Product[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
};

export type ProductQueryParams = {
  page?: number;
  limit?: number;
  search?: string;
  categories?: number[];
  inStock?: boolean;
};

export const productsService = {
  /**
   * Fetch products with optional filtering
   */
  getProducts: async (params: ProductQueryParams = {}): Promise<ProductsResponse> => {
    try {
      const { page = 1, limit = 12, search, categories, inStock } = params;

      // Build query string
      const queryParams = new URLSearchParams();
      queryParams.append('page', page.toString());
      queryParams.append('limit', limit.toString());

      if (search) {
        queryParams.append('search', search);
      }

      if (categories && categories.length > 0) {
        queryParams.append('categories', categories.join(','));
      }

      // Only add inStock parameter when it's true
      if (inStock === true) {
        queryParams.append('inStock', 'true');
      }

      const response = await api.get(`/products?${queryParams.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching products:', error);
      throw error;
    }
  },

  /**
   * Fetch a single product by ID
   */
  getProduct: async (id: number): Promise<Product> => {
    try {
      const response = await api.get(`/products/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching product ${id}:`, error);
      throw error;
    }
  },
};
