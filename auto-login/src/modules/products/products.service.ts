import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Product } from './entities/product.entity';
import { CreateProductDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
import { QueryProductDto } from './dto/query-product.dto';

@Injectable()
export class ProductsService {
  constructor(
    @InjectRepository(Product)
    private readonly productRepository: Repository<Product>,
  ) {}

  async create(createProductDto: CreateProductDto): Promise<Product> {
    const productData: any = { ...createProductDto };

    // If category_id is provided, set up the category relation
    if (createProductDto.category_id) {
      productData.category = { id: createProductDto.category_id };
      delete productData.category_id;
    }

    // Create a new product instance
    const product = this.productRepository.create(productData);

    // Save and return the product
    const result = await this.productRepository.save(product);

    // TypeORM's save method can return an array when saving multiple entities
    // but we're only saving a single entity here, so we can safely cast it
    return Array.isArray(result) ? result[0] : result;
  }

  async findAll(queryProductDto: QueryProductDto) {
    const {
      page = 1,
      limit = 10,
      search,
      categories,
      inStock,
    } = queryProductDto;
    const skip = (page - 1) * limit;

    const queryBuilder = this.productRepository
      .createQueryBuilder('product')
      .leftJoinAndSelect('product.category', 'category');

    // Apply search filter if provided
    if (search) {
      queryBuilder.andWhere('product.name ILIKE :search', {
        search: `%${search}%`,
      });
    }

    // Apply category filter if provided
    if (categories) {
      // Ensure categories is an array
      const categoriesArray = Array.isArray(categories)
        ? categories
        : [categories];

      if (categoriesArray.length > 0) {
        // Filter out invalid category IDs (0 or negative numbers)
        const validCategoryIds = categoriesArray.filter((id) => Number(id) > 0);
        if (validCategoryIds.length > 0) {
          queryBuilder.andWhere('category.id IN (:...categoryIds)', {
            categoryIds: validCategoryIds,
          });
        }
      }
    }

    // Apply in-stock filter only if inStock is true
    if (inStock === true) {
      queryBuilder.andWhere('product.quantity > 0');
    }

    // Get total count for pagination
    const total = await queryBuilder.getCount();

    // Apply pagination
    queryBuilder.skip(skip).take(limit);

    // Order by id descending (newest first)
    queryBuilder.orderBy('product.id', 'DESC');

    // Execute query
    const products = await queryBuilder.getMany();

    return {
      data: products,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: number): Promise<Product> {
    const product = await this.productRepository.findOne({
      where: { id },
      relations: ['category'],
    });
    if (!product) {
      throw new NotFoundException(`Product with ID ${id} not found`);
    }
    return product;
  }

  async update(
    id: number,
    updateProductDto: UpdateProductDto,
  ): Promise<Product> {
    const product = await this.findOne(id);

    const updateData: any = { ...updateProductDto };

    // Handle category_id update
    if (updateProductDto.category_id) {
      updateData.category = { id: updateProductDto.category_id };
      delete updateData.category_id;
    } else if (updateProductDto.category_id === null) {
      updateData.category = null;
    }

    Object.assign(product, updateData);
    const updatedProduct = await this.productRepository.save(product);

    // Return product with category relation
    return this.findOne(updatedProduct.id);
  }

  async remove(id: number): Promise<void> {
    const product = await this.findOne(id);
    await this.productRepository.remove(product);
  }
}
