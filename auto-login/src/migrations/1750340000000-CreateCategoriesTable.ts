import {
  MigrationInterface,
  QueryRunner,
  Table,
  TableColumn,
  TableForeignKey,
} from 'typeorm';

export class CreateCategoriesTable1750340000000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create categories table
    await queryRunner.createTable(
      new Table({
        name: 'categories',
        columns: [
          {
            name: 'id',
            type: 'int',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'name',
            type: 'varchar',
            length: '100',
            isUnique: true,
          },
          {
            name: 'description',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'color',
            type: 'varchar',
            length: '50',
            isNullable: true,
          },
          {
            name: 'icon',
            type: 'varchar',
            length: '255',
            isNullable: true,
          },
          {
            name: 'created_at',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updated_at',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true,
    );

    // Drop the old category column from products table
    await queryRunner.dropColumn('products', 'category');

    // Add category_id column to products table
    await queryRunner.addColumn(
      'products',
      new TableColumn({
        name: 'category_id',
        type: 'int',
        isNullable: true,
      }),
    );

    // Create foreign key constraint
    await queryRunner.createForeignKey(
      'products',
      new TableForeignKey({
        columnNames: ['category_id'],
        referencedTableName: 'categories',
        referencedColumnNames: ['id'],
        onDelete: 'SET NULL',
        onUpdate: 'CASCADE',
      }),
    );

    // Insert some sample categories
    await queryRunner.query(`
      INSERT INTO categories (name, description, color, icon) VALUES
      ('AI Tools', 'Artificial Intelligence and Machine Learning tools', '#3B82F6', 'ai'),
      ('Cloud Storage', 'Cloud storage and file management services', '#10B981', 'cloud'),
      ('Productivity', 'Tools to boost productivity and efficiency', '#F59E0B', 'productivity'),
      ('Development', 'Software development and programming tools', '#8B5CF6', 'code'),
      ('Design', 'Design and creative tools', '#EF4444', 'design'),
      ('Marketing', 'Marketing and advertising tools', '#F97316', 'marketing'),
      ('Security', 'Security and privacy tools', '#6B7280', 'security'),
      ('Communication', 'Communication and collaboration tools', '#06B6D4', 'communication')
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop foreign key constraint
    const table = await queryRunner.getTable('products');
    const foreignKey = table?.foreignKeys.find(
      (fk) => fk.columnNames.indexOf('category_id') !== -1,
    );
    if (foreignKey) {
      await queryRunner.dropForeignKey('products', foreignKey);
    }

    // Drop category_id column from products table
    await queryRunner.dropColumn('products', 'category_id');

    // Add back the old category column
    await queryRunner.addColumn(
      'products',
      new TableColumn({
        name: 'category',
        type: 'varchar',
        isNullable: true,
      }),
    );

    // Drop categories table
    await queryRunner.dropTable('categories');
  }
}
