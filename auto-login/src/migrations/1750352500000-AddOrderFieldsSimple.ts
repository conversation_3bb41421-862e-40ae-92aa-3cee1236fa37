import { MigrationInterface, QueryRunner } from 'typeorm';

export class Add<PERSON>rderFieldsSimple1750352500000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE "orders" 
      ADD COLUMN "customer_email" character varying,
      ADD COLUMN "customer_name" character varying,
      ADD COLUMN "customer_phone" character varying,
      ADD COLUMN "payment_status" character varying NOT NULL DEFAULT 'unpaid',
      ADD COLUMN "payment_method" character varying,
      ADD COLUMN "subtotal" numeric(10,2) NOT NULL DEFAULT '0';
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE "orders" 
      DROP COLUMN "customer_email",
      DROP COLUMN "customer_name",
      DROP COLUMN "customer_phone",
      DROP COLUMN "payment_status",
      DROP COLUMN "payment_method",
      DROP COLUMN "subtotal";
    `);
  }
}
