'use client';

import { useTranslations } from 'next-intl';
import React from 'react';
import ShopHeader from './ShopHeader';

type ShopLayoutProps = {
  children: React.ReactNode;
};

const ShopLayout: React.FC<ShopLayoutProps> = ({ children }) => {
  const t = useTranslations('ShopLayout');

  return (
    <div className="flex min-h-screen flex-col bg-gray-50">
      <ShopHeader />
      <main className="flex-grow">{children}</main>
      <footer className="bg-white py-4 text-center text-sm text-gray-600">
        {t('footer', { currentYear: new Date().getFullYear() })}
      </footer>
    </div>
  );
};

export default ShopLayout; 