<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Admin - Create Product</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/toastify-js@1.12.0/src/toastify.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/toastify-js@1.12.0/src/toastify.min.css">
  </head>
  <body class="bg-gray-100 min-h-screen p-8">
    <div class="max-w-4xl mx-auto">
      <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900">Create New Product</h1>
        <div class="space-x-2">
          <button id="backBtn" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
            Back to Products
          </button>
        </div>
      </div>

      <div class="bg-white shadow rounded-lg overflow-hidden">
        <div class="p-6">
          <div id="errorMessage" class="hidden mb-4 p-4 text-red-700 bg-red-100 rounded-md"></div>

          <form id="createProductForm" class="space-y-6">
            <!-- Basic Information -->
            <div>
              <h2 class="text-xl font-semibold mb-4">Basic Information</h2>
              
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label for="name" class="block text-sm font-medium text-gray-700">Name</label>
                  <input 
                    type="text" 
                    id="name" 
                    name="name" 
                    required
                    class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  />
                </div>

                <div>
                  <label for="category_id" class="block text-sm font-medium text-gray-700">Category</label>
                  <select 
                    id="category_id" 
                    name="category_id"
                    class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  >
                    <option value="">Select a category</option>
                    <!-- Categories will be loaded dynamically -->
                  </select>
                </div>

                <div>
                  <label for="original_price" class="block text-sm font-medium text-gray-700">Original Price</label>
                  <div class="mt-1 relative rounded-md shadow-sm">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <span class="text-gray-500 sm:text-sm">$</span>
                    </div>
                    <input 
                      type="number" 
                      step="0.01" 
                      min="0" 
                      id="original_price" 
                      name="original_price" 
                      required
                      class="pl-7 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                    />
                  </div>
                </div>

                <div>
                  <label for="discount_price" class="block text-sm font-medium text-gray-700">Discount Price (Optional)</label>
                  <div class="mt-1 relative rounded-md shadow-sm">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <span class="text-gray-500 sm:text-sm">$</span>
                    </div>
                    <input 
                      type="number" 
                      step="0.01" 
                      min="0" 
                      id="discount_price" 
                      name="discount_price"
                      class="pl-7 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                    />
                  </div>
                </div>

                <div>
                  <label for="quantity" class="block text-sm font-medium text-gray-700">Quantity</label>
                  <input 
                    type="number" 
                    min="0" 
                    id="quantity" 
                    name="quantity" 
                    required
                    value="0"
                    class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  />
                </div>

                <div>
                  <label for="image_url" class="block text-sm font-medium text-gray-700">Product Image</label>
                  <div class="mt-1 space-y-2">
                    <!-- File upload option -->
                    <div>
                      <label class="block text-xs text-gray-500 mb-1">Upload from computer</label>
                      <input
                        type="file"
                        id="image_file"
                        accept="image/*"
                        class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                      />
                      <p class="text-xs text-gray-500 mt-1">Upload an image file (max 1MB)</p>
                    </div>
                    
                    <!-- URL input option -->
                    <div>
                      <label class="block text-xs text-gray-500 mb-1">Or enter image URL</label>
                      <input 
                        type="url" 
                        id="image_url" 
                        name="image_url"
                        class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                        placeholder="https://example.com/image.jpg"
                      />
                    </div>
                    
                    <!-- Image preview -->
                    <div class="mt-2">
                      <div id="image_preview_container" class="hidden">
                        <div class="flex items-center space-x-2">
                          <img
                            id="image_preview"
                            src=""
                            class="h-32 object-contain border rounded"
                            alt="Product image preview"
                          />
                          <button
                            type="button"
                            id="clear_image_btn"
                            class="bg-red-500 hover:bg-red-700 text-white text-sm py-1 px-2 rounded"
                          >
                            Remove
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Description -->
            <div>
              <h2 class="text-xl font-semibold mb-4">Description</h2>
              <div>
                <label for="description" class="block text-sm font-medium text-gray-700">Product Description</label>
                <textarea 
                  id="description" 
                  name="description" 
                  rows="4"
                  class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                ></textarea>
              </div>
            </div>

            <!-- Features -->
            <div>
              <h2 class="text-xl font-semibold mb-4">Features</h2>
              <div class="space-y-2">
                <div id="features-container">
                  <!-- Features will be added here dynamically -->
                </div>
                <button 
                  type="button" 
                  id="addFeatureBtn"
                  class="mt-2 inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  Add Feature
                </button>
              </div>
            </div>

            <!-- Submit Button -->
            <div class="flex justify-end">
              <button 
                type="submit"
                class="px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Create Product
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <script>
      let features = [];
      
      // Setup axios with credentials
      function setupAxios() {
        // Set axios to send cookies with all requests
        axios.defaults.withCredentials = true;
        return true;
      }
      
      // Load categories
      async function loadCategories() {
        try {
          const response = await axios.get('/products/categories');
          const categories = response.data;
          const categorySelect = document.getElementById('category_id');
          
          categories.forEach(category => {
            const option = document.createElement('option');
            option.value = category.id;
            option.textContent = category.name;
            categorySelect.appendChild(option);
          });
        } catch (error) {
          console.error('Error loading categories:', error);
        }
      }

      // Check authentication and admin role
      async function checkAuth() {
        try {
          const response = await axios.get('/auth/status', { withCredentials: true });
          if (!response.data.isAuthenticated) {
            window.location.href = '/login';
            return false;
          }

          // Check if user has admin role
          if (response.data.user && response.data.user.role !== 'admin') {
            // User is authenticated but not an admin
            window.location.href = '/login';
            return false;
          }

          return true;
        } catch (error) {
          window.location.href = '/login';
          return false;
        }
      }

      // Show toast notification
      function showToast(message, type = 'success') {
        let backgroundColor;
        switch (type) {
          case 'success':
            backgroundColor = '#48bb78';
            break;
          case 'error':
            backgroundColor = '#f56565';
            break;
          case 'info':
            backgroundColor = '#4299e1';
            break;
          default:
            backgroundColor = '#48bb78';
        }

        Toastify({
          text: message,
          duration: 3000,
          close: true,
          gravity: "top",
          position: "right",
          backgroundColor,
          stopOnFocus: true
        }).showToast();
      }

      // Function to handle image selection and convert to base64
      function handleImageSelect(event) {
        const file = event.target.files[0];
        if (!file) return;

        // Check file size (limit to 1MB)
        if (file.size > 1024 * 1024) {
          showToast('Image size should be less than 1MB', 'error');
          event.target.value = '';
          return;
        }

        // Check file type
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
        if (!allowedTypes.includes(file.type)) {
          showToast('Please select a valid image file (JPG, PNG, GIF, WEBP)', 'error');
          event.target.value = '';
          return;
        }

        // Create FormData for file upload
        const formData = new FormData();
        formData.append('image', file);

        // Upload to server
        uploadImageToServer(formData, file.name);
      }

      // Function to upload image to server
      async function uploadImageToServer(formData, originalName) {
        try {
          const response = await axios.post('/upload/image', formData, {
            headers: {
              'Content-Type': 'multipart/form-data'
            },
            timeout: 30000, // 30 second timeout
            withCredentials: true // Ensure cookies are sent
          });

          if (response.data && response.data.success && response.data.imageUrl) {
            console.log('Image uploaded successfully:', response.data.imageUrl);
            
            // Set the image URL in the input field
            document.getElementById('image_url').value = response.data.imageUrl;
            document.getElementById('image_preview').src = response.data.imageUrl;
            document.getElementById('image_preview_container').classList.remove('hidden');
            
            showToast('Image uploaded successfully!', 'success');
          } else {
            throw new Error('Invalid response from server');
          }
        } catch (error) {
          console.error('Image upload failed:', error);
          let errorMessage = 'Failed to upload image';

          if (error.response) {
            errorMessage = error.response.data?.message || errorMessage;
          } else if (error.request) {
            errorMessage = 'Network error - please check your connection';
          }

          showToast(errorMessage, 'error');
          
          // Clear the file input on error
          document.getElementById('image_file').value = '';
        }
      }

      // Function to handle URL input change
      function handleImageUrlChange(event) {
        const url = event.target.value.trim();
        const preview = document.getElementById('image_preview');
        const container = document.getElementById('image_preview_container');
        
        if (url && !url.startsWith('data:')) { // Don't try to load base64 as external URL
          // Test if URL is valid and image loads
          const img = new Image();
          img.onload = function() {
            preview.src = url;
            container.classList.remove('hidden');
          };
          img.onerror = function() {
            container.classList.add('hidden');
            showToast('Invalid image URL or image failed to load', 'error');
          };
          img.src = url;
        } else if (url.startsWith('data:')) {
          // If it's base64 data, just show it
          preview.src = url;
          container.classList.remove('hidden');
        } else {
          container.classList.add('hidden');
        }
      }

      // Function to clear image
      function clearImage() {
        document.getElementById('image_url').value = '';
        document.getElementById('image_file').value = '';
        document.getElementById('image_preview').src = '';
        document.getElementById('image_preview_container').classList.add('hidden');
        showToast('Image removed', 'success');
      }

      // Show error message
      function showError(message) {
        const errorMessage = document.getElementById('errorMessage');
        errorMessage.textContent = message;
        errorMessage.classList.remove('hidden');
        showToast(message, 'error');
      }

      // Render features inputs
      function renderFeatures() {
        const container = document.getElementById('features-container');
        container.innerHTML = '';

        features.forEach((feature, index) => {
          const featureRow = document.createElement('div');
          featureRow.className = 'flex items-center space-x-2';
          
          featureRow.innerHTML = `
            <input 
              type="text" 
              name="features[]" 
              value="${feature}" 
              class="flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            />
            <button 
              type="button" 
              class="remove-feature text-red-600 hover:text-red-800" 
              data-index="${index}"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
              </svg>
            </button>
          `;
          
          container.appendChild(featureRow);
        });

        // Add event listeners for remove buttons
        document.querySelectorAll('.remove-feature').forEach(button => {
          button.addEventListener('click', function() {
            const index = parseInt(this.getAttribute('data-index'));
            features.splice(index, 1);
            renderFeatures();
          });
        });
      }

      // Add new feature
      function addFeature() {
        features.push('');
        renderFeatures();
        
        // Focus the last input
        const inputs = document.querySelectorAll('input[name="features[]"]');
        if (inputs.length > 0) {
          inputs[inputs.length - 1].focus();
        }
      }

      // Create product
      async function createProduct(formData) {
        if (!setupAxios()) return;

        try {
          // Get features from inputs
          const featureInputs = document.querySelectorAll('input[name="features[]"]');
          const productFeatures = Array.from(featureInputs).map(input => input.value.trim()).filter(Boolean);
          
          // Create payload
          const payload = {
            name: formData.get('name'),
            original_price: parseFloat(formData.get('original_price')),
            quantity: parseInt(formData.get('quantity')),
            description: formData.get('description'),
            features: productFeatures,
          };
          
          // Add category_id if selected
          const categoryId = formData.get('category_id');
          if (categoryId) {
            payload.category_id = parseInt(categoryId);
          }
          
          // Add optional fields if they have values
          const discountPrice = formData.get('discount_price');
          if (discountPrice) {
            payload.discount_price = parseFloat(discountPrice);
          }
          
          const imageUrl = formData.get('image_url');
          if (imageUrl) {
            payload.image_url = imageUrl;
          }

          const response = await axios.post('/products', payload);
          showToast('Product created successfully');
          
          // Redirect to product details page after a short delay
          setTimeout(() => {
            window.location.href = `/admin/products/${response.data.id}`;
          }, 1500);
        } catch (error) {
          if (error.response && error.response.status === 401) {
            window.location.href = '/login';
          } else {
            const message = error.response?.data?.message || 'Failed to create product. Please try again.';
            showError(message);
          }
        }
      }

      // Event listeners
      document.addEventListener('DOMContentLoaded', async () => {
        // Initial setup
        setupAxios();
        await checkAuth();
        
        // Load categories
        await loadCategories();

        // Add feature button
        document.getElementById('addFeatureBtn').addEventListener('click', addFeature);

        // Image upload and URL input handlers
        document.getElementById('image_file').addEventListener('change', handleImageSelect);
        document.getElementById('image_url').addEventListener('input', handleImageUrlChange);
        document.getElementById('clear_image_btn').addEventListener('click', clearImage);

        // Back button
        document.getElementById('backBtn').addEventListener('click', () => {
          window.location.href = '/admin/products';
        });

        // Form submission
        document.getElementById('createProductForm').addEventListener('submit', async (e) => {
          e.preventDefault();
          const formData = new FormData(e.target);
          await createProduct(formData);
        });

        // Add initial feature field
        addFeature();
      });
    </script>
  </body>
</html> 