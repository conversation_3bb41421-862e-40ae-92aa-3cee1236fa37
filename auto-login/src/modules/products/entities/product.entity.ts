import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedC<PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  OneToOne,
  JoinColumn,
} from 'typeorm';
import { Category } from './category.entity';

@Entity('products')
export class Product {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @Column('text', { nullable: true })
  description: string;

  @Column('decimal', { name: 'original_price', precision: 10, scale: 2 })
  original_price: number;

  @Column('decimal', {
    name: 'discount_price',
    precision: 10,
    scale: 2,
    default: 0,
  })
  discount_price: number;

  @Column({ default: 0 })
  quantity: number;

  @OneToOne(() => Category, (category) => category.product, { nullable: true })
  @JoinColumn({ name: 'category_id' })
  category: Category;

  @Column({ name: 'image_url', nullable: true })
  image_url: string;

  @Column('jsonb', { default: [] })
  features: string[];

  @CreateDateColumn({ name: 'created_at' })
  created_at: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updated_at: Date;
}
