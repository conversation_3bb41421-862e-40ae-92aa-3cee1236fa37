import type { Product } from '@/services/products';
import Image from 'next/image';
import React, { useState } from 'react';

type ProductCardProps = {
  product: Product;
  onAddToCart: (product: Product, quantity: number) => void;
};

const ProductCard: React.FC<ProductCardProps> = ({ product, onAddToCart }) => {
  const [quantity, setQuantity] = useState(1);
  const {
    name,
    description,
    original_price,
    discount_price,
    quantity: stockQuantity,
    image_url,
    features,
  } = product;

  const isOutOfStock = stockQuantity <= 0;
  const hasDiscount = discount_price > 0 && discount_price < original_price;
  const displayPrice = hasDiscount ? Number(discount_price) : Number(original_price);
  const discountPercentage = hasDiscount
    ? Math.round(((Number(original_price) - Number(discount_price)) / Number(original_price)) * 100)
    : 0;

  const handleIncrement = () => {
    if (quantity < stockQuantity) {
      setQuantity(prev => prev + 1);
    }
  };

  const handleDecrement = () => {
    if (quantity > 1) {
      setQuantity(prev => prev - 1);
    }
  };

  const handleAddToCart = () => {
    if (!isOutOfStock) {
      onAddToCart(product, quantity);
      setQuantity(1); // Reset quantity after adding to cart
    }
  };

  return (
    <div className="overflow-hidden rounded-lg border border-gray-200 bg-white shadow-md">
      <div className="relative h-48 w-full">
        {image_url
          ? (
              <Image
                src={image_url}
                alt={name}
                fill
                className="object-cover"
              />
            )
          : (
              <div className="flex size-full items-center justify-center bg-gray-200">
                <span className="text-gray-400">No image</span>
              </div>
            )}
        {isOutOfStock && (
          <div className="absolute right-0 top-0 m-2 rounded bg-red-500 px-2 py-1 text-white">
            Sold Out
          </div>
        )}
        {hasDiscount && (
          <div className="absolute left-0 top-0 m-2 rounded bg-green-500 px-2 py-1 text-white">
            -
            {discountPercentage}
            %
          </div>
        )}
      </div>

      <div className="p-4">
        <h3 className="mb-1 truncate text-lg font-semibold">{name}</h3>

        {description && (
          <p className="mb-2 line-clamp-2 text-sm text-gray-600">{description}</p>
        )}

        <div className="mb-3">
          <div className="flex items-center gap-2">
            <span className={`text-lg font-bold ${hasDiscount ? 'text-red-600' : 'text-gray-900'}`}>
              $
              {displayPrice.toFixed(2)}
            </span>
            {hasDiscount && (
              <span className="text-sm text-gray-500 line-through">
                $
                {Number(original_price).toFixed(2)}
              </span>
            )}
          </div>
        </div>

        {features && features.length > 0 && (
          <div className="mb-3">
            <h4 className="mb-1 text-sm font-medium">Features:</h4>
            <ul className="list-disc pl-4 text-xs text-gray-600">
              {features.slice(0, 3).map((feature, index) => (
                <li key={index} className="line-clamp-1">{feature}</li>
              ))}
              {features.length > 3 && (
                <li className="text-blue-500">
                  +
                  {features.length - 3}
                  {' '}
                  more
                </li>
              )}
            </ul>
          </div>
        )}

        {/* Quantity selector */}
        <div className="mb-3 flex items-center justify-between">
          <span className="text-sm font-medium text-gray-700">Quantity:</span>
          <div className="flex items-center">
            <button
              type="button"
              onClick={handleDecrement}
              disabled={quantity <= 1 || isOutOfStock}
              className={`flex h-8 w-8 items-center justify-center rounded-l border border-gray-300 ${
                quantity <= 1 || isOutOfStock ? 'cursor-not-allowed bg-gray-100 text-gray-400' : 'bg-gray-50 hover:bg-gray-100'
              }`}
              aria-label="Decrease quantity"
            >
              -
            </button>
            <div className="flex h-8 w-10 items-center justify-center border-y border-gray-300 bg-white text-center text-sm">
              {quantity}
            </div>
            <button
              type="button"
              onClick={handleIncrement}
              disabled={quantity >= stockQuantity || isOutOfStock}
              className={`flex h-8 w-8 items-center justify-center rounded-r border border-gray-300 ${
                quantity >= stockQuantity || isOutOfStock ? 'cursor-not-allowed bg-gray-100 text-gray-400' : 'bg-gray-50 hover:bg-gray-100'
              }`}
              aria-label="Increase quantity"
            >
              +
            </button>
          </div>
        </div>

        <button
          type="button"
          onClick={handleAddToCart}
          disabled={isOutOfStock}
          className={`w-full rounded-md px-4 py-2 font-medium text-white transition ${
            isOutOfStock
              ? 'cursor-not-allowed bg-gray-400'
              : 'bg-blue-600 hover:bg-blue-700'
          }`}
        >
          {isOutOfStock ? 'Out of Stock' : 'Add to Cart'}
        </button>
      </div>
    </div>
  );
};

export default ProductCard;
