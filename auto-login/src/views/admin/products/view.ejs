<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Admin - View Product</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/toastify-js@1.12.0/src/toastify.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/toastify-js@1.12.0/src/toastify.min.css">
  </head>
  <body class="bg-gray-100 min-h-screen p-8">
    <div class="max-w-4xl mx-auto">
      <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900">Product Details</h1>
        <div class="space-x-2">
          <button id="editBtn" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
            Edit Product
          </button>
          <button id="backBtn" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
            Back to Products
          </button>
        </div>
      </div>

      <div class="bg-white shadow rounded-lg overflow-hidden mb-6">
        <div class="p-6">
          <div id="errorMessage" class="hidden mb-4 p-4 text-red-700 bg-red-100 rounded-md"></div>

          <!-- Product Details -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Product Image -->
            <div id="image-preview-container" class="mb-6">
              <h2 class="text-xl font-semibold mb-4">Product Image</h2>
              <div class="border rounded-lg overflow-hidden h-64 bg-gray-100 flex items-center justify-center">
                <img id="product-image" src="" alt="Product Image" class="w-full h-full object-contain" />
              </div>
            </div>

            <div>
              <h2 class="text-xl font-semibold mb-4">Basic Information</h2>

              <div class="space-y-3">
                <div>
                  <p class="text-sm font-medium text-gray-500">ID</p>
                  <p id="product-id" class="mt-1 text-sm text-gray-900"></p>
                </div>

                <div>
                  <p class="text-sm font-medium text-gray-500">Name</p>
                  <p id="product-name" class="mt-1 text-sm text-gray-900"></p>
                </div>

                <div>
                  <p class="text-sm font-medium text-gray-500">Category</p>
                  <p id="product-category" class="mt-1 text-sm text-gray-900"></p>
                </div>

                <div>
                  <p class="text-sm font-medium text-gray-500">Original Price</p>
                  <p id="product-original-price" class="mt-1 text-sm text-gray-900"></p>
                </div>

                <div>
                  <p class="text-sm font-medium text-gray-500">Discount Price</p>
                  <p id="product-discount-price" class="mt-1 text-sm text-gray-900"></p>
                </div>

                <div>
                  <p class="text-sm font-medium text-gray-500">Quantity</p>
                  <p id="product-quantity" class="mt-1 text-sm text-gray-900"></p>
                </div>

                <div>
                  <p class="text-sm font-medium text-gray-500">Status</p>
                  <p id="product-status" class="mt-1 text-sm text-gray-900"></p>
                </div>
              </div>
            </div>
          </div>

          <!-- Product Description -->
          <div class="mt-6">
            <h2 class="text-xl font-semibold mb-4">Description</h2>
            <div class="bg-gray-50 p-4 rounded-lg">
              <p id="product-description" class="text-sm text-gray-700 whitespace-pre-line"></p>
            </div>
          </div>

          <!-- Product Features -->
          <div class="mt-6">
            <h2 class="text-xl font-semibold mb-4">Features</h2>
            <div class="bg-gray-50 p-4 rounded-lg">
              <ul id="product-features" class="list-disc pl-5 text-sm text-gray-700 space-y-1">
                <!-- Features will be populated here -->
              </ul>
            </div>
          </div>

          <!-- Category Information -->
          <div id="category-section" class="mt-6 hidden">
            <h2 class="text-xl font-semibold mb-4">Category Information</h2>
            <div class="bg-gray-50 p-4 rounded-lg">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p class="text-sm font-medium text-gray-500">Category Name</p>
                  <p id="category-name" class="mt-1 text-sm text-gray-900"></p>
                </div>
                <div>
                  <p class="text-sm font-medium text-gray-500">Color</p>
                  <div class="mt-1 flex items-center space-x-2">
                    <div id="category-color-preview" class="w-4 h-4 rounded border border-gray-300"></div>
                    <span id="category-color" class="text-sm text-gray-900"></span>
                  </div>
                </div>
                <div class="md:col-span-2">
                  <p class="text-sm font-medium text-gray-500">Description</p>
                  <p id="category-description" class="mt-1 text-sm text-gray-700"></p>
                </div>
              </div>
            </div>
          </div>

          <!-- Created/Updated Info -->
          <div class="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <p class="text-sm font-medium text-gray-500">Created At</p>
              <p id="product-created" class="mt-1 text-sm text-gray-900"></p>
            </div>

            <div>
              <p class="text-sm font-medium text-gray-500">Updated At</p>
              <p id="product-updated" class="mt-1 text-sm text-gray-900"></p>
            </div>
          </div>

          <!-- Actions -->
          <div class="mt-6">
            <h2 class="text-xl font-semibold mb-4">Actions</h2>
            <div class="flex space-x-2">
              <button id="deleteBtn" class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete Product
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center hidden z-50">
      <div class="bg-white rounded-lg p-6 max-w-md mx-auto">
        <h3 class="text-xl font-semibold mb-4">Confirm Delete</h3>
        <p class="mb-6">Are you sure you want to delete this product? This action cannot be undone.</p>
        <div class="flex justify-end space-x-2">
          <button id="cancelDeleteBtn" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
            Cancel
          </button>
          <button id="confirmDeleteBtn" class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
            Delete
          </button>
        </div>
      </div>
    </div>

    <script>
      // Get product ID from server-rendered data
      const productId = `<%- id %>`; // Use the ID passed from the server
      
      // Setup axios with credentials
      function setupAxios() {
        // Set axios to send cookies with all requests
        axios.defaults.withCredentials = true;
        return true;
      }

      // Check authentication and admin role
      async function checkAuth() {
        try {
          const response = await axios.get('/auth/status', { withCredentials: true });
          if (!response.data.isAuthenticated) {
            window.location.href = '/login';
            return false;
          }

          // Check if user has admin role
          if (response.data.user && response.data.user.role !== 'admin') {
            // User is authenticated but not an admin
            window.location.href = '/login';
            return false;
          }

          return true;
        } catch (error) {
          window.location.href = '/login';
          return false;
        }
      }

      // Show toast notification
      function showToast(message, type = 'success') {
        const backgroundColor = type === 'success' ? '#48bb78' : '#f56565';

        Toastify({
          text: message,
          duration: 3000,
          close: true,
          gravity: "top",
          position: "right",
          backgroundColor,
          stopOnFocus: true
        }).showToast();
      }

      // Show error message
      function showError(message) {
        const errorMessage = document.getElementById('errorMessage');
        errorMessage.textContent = message;
        errorMessage.classList.remove('hidden');
        showToast(message, 'error');
      }

      // Format date
      function formatDate(dateString) {
        if (!dateString) return 'N/A';
        return new Date(dateString).toLocaleString();
      }

      // Format price
      function formatPrice(price) {
        if (!price) return '$0.00';
        return '$' + Number(price).toFixed(2);
      }

      // Fetch product details
      async function fetchProduct(id) {
        if (!setupAxios()) return;

        try {
          const response = await axios.get(`/products/${id}`);
          return response.data;
        } catch (error) {
          if (error.response && error.response.status === 401) {
            window.location.href = '/login';
          } else {
            const message = error.response?.data?.message || 'Failed to fetch product details. Please try again.';
            showError(message);
          }
          return null;
        }
      }

      // Display product details
      function displayProductDetails(product) {
        document.getElementById('product-id').textContent = product.id;
        document.getElementById('product-name').textContent = product.name || 'N/A';
        document.getElementById('product-category').textContent = product.category ? product.category.name : 'No category';
        document.getElementById('product-original-price').textContent = formatPrice(product.original_price);
        document.getElementById('product-discount-price').textContent = product.discount_price ? formatPrice(product.discount_price) : 'No discount';
        document.getElementById('product-quantity').textContent = product.quantity;
        
        const isInStock = product.quantity > 0;
        document.getElementById('product-status').textContent = isInStock ? 'In Stock' : 'Out of Stock';
        document.getElementById('product-status').className = isInStock ? 'mt-1 text-sm text-green-600 font-medium' : 'mt-1 text-sm text-red-600 font-medium';
        
        document.getElementById('product-description').textContent = product.description || 'No description available';
        document.getElementById('product-created').textContent = formatDate(product.created_at);
        document.getElementById('product-updated').textContent = formatDate(product.updated_at);

        // Display image if available
        if (product.image_url) {
          document.getElementById('product-image').src = product.image_url;
        } else {
          document.getElementById('product-image').src = 'https://via.placeholder.com/400x400?text=No+Image';
        }

        // Display features
        const featuresContainer = document.getElementById('product-features');
        featuresContainer.innerHTML = '';

        if (product.features && product.features.length > 0) {
          product.features.forEach(feature => {
            const li = document.createElement('li');
            li.textContent = feature;
            featuresContainer.appendChild(li);
          });
        } else {
          featuresContainer.innerHTML = '<li>No features available</li>';
        }

        // Display category information
        const categorySection = document.getElementById('category-section');
        if (product.category) {
          categorySection.classList.remove('hidden');
          document.getElementById('category-name').textContent = product.category.name;
          document.getElementById('category-description').textContent = product.category.description || 'No description available';
          
          // Display category color
          const categoryColor = product.category.color || '#6B7280';
          document.getElementById('category-color').textContent = categoryColor;
          document.getElementById('category-color-preview').style.backgroundColor = categoryColor;
        } else {
          categorySection.classList.add('hidden');
        }
      }

      // Delete product
      async function deleteProduct(id) {
        if (!setupAxios()) return;

        try {
          await axios.delete(`/products/${id}`);
          showToast('Product deleted successfully');
          setTimeout(() => {
            window.location.href = '/admin/products';
          }, 1500);
        } catch (error) {
          if (error.response && error.response.status === 401) {
            window.location.href = '/login';
          } else {
            const message = error.response?.data?.message || 'Failed to delete product. Please try again.';
            showToast(message, 'error');
          }
        }
      }

      // Show delete modal
      function showDeleteModal() {
        document.getElementById('deleteModal').classList.remove('hidden');
      }

      // Hide delete modal
      function hideDeleteModal() {
        document.getElementById('deleteModal').classList.add('hidden');
      }

      // Event listeners
      document.addEventListener('DOMContentLoaded', async () => {
        // Initial setup
        setupAxios();
        await checkAuth();

        // Fetch and display product details
        if (productId) {
          const product = await fetchProduct(productId);
          if (product) {
            displayProductDetails(product);
          }
        } else {
          showError('Product ID not found in URL');
        }

        // Button event listeners
        document.getElementById('editBtn').addEventListener('click', () => {
          window.location.href = `/admin/products/${productId}/edit`;
        });

        document.getElementById('backBtn').addEventListener('click', () => {
          window.location.href = '/admin/products';
        });

        document.getElementById('deleteBtn').addEventListener('click', showDeleteModal);
        document.getElementById('cancelDeleteBtn').addEventListener('click', hideDeleteModal);
        document.getElementById('confirmDeleteBtn').addEventListener('click', () => {
          deleteProduct(productId);
        });
      });
    </script>
  </body>
</html> 