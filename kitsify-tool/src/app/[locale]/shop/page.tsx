'use client';

import type { Product, ProductsResponse } from '@/services/products';
import { useTranslations } from 'next-intl';
import React, { useEffect, useState } from 'react';
import FilterSidebar from '@/components/FilterSidebar';
import ProductCard from '@/components/ProductCard';
import ShopLayout from '@/components/ShopLayout';
import { useCart } from '@/contexts/CartContext';
import { productsService } from '@/services/products';

function ShopPageContent() {
  const t = useTranslations('Shop');
  const { addToCart } = useCart();

  // State for products and pagination
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [meta, setMeta] = useState<{ total: number; page: number; limit: number; totalPages: number }>({
    total: 0,
    page: 1,
    limit: 12,
    totalPages: 0,
  });

  // State for filters
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [selectedCategories, setSelectedCategories] = useState<number[]>([]);

  // Load products on mount and when filters change
  useEffect(() => {
    const fetchProducts = async () => {
      setLoading(true);
      try {
        const response: ProductsResponse = await productsService.getProducts({
          page: meta.page,
          limit: meta.limit,
          search: searchTerm,
          categories: selectedCategories,
        });

        setProducts(response.data);
        setMeta(response.meta);
        setError(null);
      } catch (err) {
        console.error('Error fetching products:', err);
        setError('Failed to load products. Please try again later.');
        setProducts([]);
      } finally {
        setLoading(false);
      }
    };

    // Debounce search term changes
    const timerId = setTimeout(() => {
      fetchProducts();
    }, 500);

    return () => clearTimeout(timerId);
  }, [meta.page, meta.limit, searchTerm, selectedCategories]);

  // Handle page change
  const handlePageChange = (page: number) => {
    setMeta(prev => ({ ...prev, page }));
  };

  // Handle adding product to cart
  const handleAddToCart = (product: Product, quantity: number = 1) => {
    addToCart(product, quantity);
  };

  // Pagination UI
  const renderPagination = () => {
    const pages = [];
    for (let i = 1; i <= meta.totalPages; i++) {
      pages.push(
        <button
          type="button"
          key={i}
          onClick={() => handlePageChange(i)}
          className={`rounded px-3 py-1 ${
            meta.page === i
              ? 'bg-blue-600 text-white'
              : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
          }`}
        >
          {i}
        </button>,
      );
    }

    return (
      <div className="mt-8 flex justify-center gap-2">
        <button
          type="button"
          onClick={() => handlePageChange(Math.max(1, meta.page - 1))}
          disabled={meta.page === 1}
          className="rounded bg-gray-200 px-3 py-1 text-gray-700 hover:bg-gray-300 disabled:opacity-50"
        >
          &lt;
        </button>
        {pages}
        <button
          type="button"
          onClick={() => handlePageChange(Math.min(meta.totalPages, meta.page + 1))}
          disabled={meta.page === meta.totalPages}
          className="rounded bg-gray-200 px-3 py-1 text-gray-700 hover:bg-gray-300 disabled:opacity-50"
        >
          &gt;
        </button>
      </div>
    );
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8 flex items-center justify-between">
        <h1 className="text-3xl font-bold">{t('title')}</h1>
      </div>

      <div className="flex flex-col gap-8 md:flex-row">
        {/* Sidebar */}
        <div className="md:w-1/4">
          <FilterSidebar
            selectedCategories={selectedCategories}
            onCategoryChange={setSelectedCategories}
            searchTerm={searchTerm}
            onSearchChange={setSearchTerm}
          />
        </div>

        {/* Main content */}
        <div className="md:w-3/4">
          {loading
            ? (
                <div className="flex h-64 items-center justify-center">
                  <div className="size-12 animate-spin rounded-full border-y-2 border-blue-500"></div>
                </div>
              )
            : error
              ? (
                  <div className="rounded border border-red-400 bg-red-100 px-4 py-3 text-red-700">
                    {error}
                  </div>
                )
              : products.length === 0
                ? (
                    <div className="py-12 text-center">
                      <h2 className="mb-2 text-xl font-semibold">{t('noProducts')}</h2>
                      <p className="text-gray-600">{t('tryDifferentFilters')}</p>
                    </div>
                  )
                : (
                    <>
                      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
                        {products.map(product => (
                          <ProductCard
                            key={product.id}
                            product={product}
                            onAddToCart={handleAddToCart}
                          />
                        ))}
                      </div>

                      {meta.totalPages > 1 && renderPagination()}
                    </>
                  )}
        </div>
      </div>
    </div>
  );
}

export default function ShopPage() {
  return (
    <ShopLayout>
      <ShopPageContent />
    </ShopLayout>
  );
}
